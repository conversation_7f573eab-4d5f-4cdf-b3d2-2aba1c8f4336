import os
from psd_tools import PSDImage
from PIL import Image

def convert_psd_to_pdf(folder_path):
    for file_name in os.listdir(folder_path):
        if file_name.lower().endswith(".psd"):
            psd_path = os.path.join(folder_path, file_name)
            pdf_path = os.path.join(folder_path, os.path.splitext(file_name)[0] + ".pdf")

            try:
                # Open PSD
                psd = PSDImage.open(psd_path)
                # Render as PIL image
                composite = psd.compose()
                # Save as PDF
                composite.save(pdf_path, "PDF", resolution=300.0)
                print(f"✅ Converted: {file_name} → {pdf_path}")
            except Exception as e:
                print(f"❌ Failed to convert {file_name}: {e}")

# Example usage
if __name__ == "__main__":
    folder = r"/Users/<USER>/Downloads/Certs"  # change this path
    convert_psd_to_pdf(folder)
